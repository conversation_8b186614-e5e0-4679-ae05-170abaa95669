const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Default settings structure that should match the database
const defaultSettings = {
  payments: {
    enabled: false,
    provider: 'disabled',
    dokuEnabled: false,
    testMode: true,
    currency: 'idr',
    commissionRate: 0.25,
    minimumAmount: 10000,
    maximumAmount: 100000000,
    dokuClientId: '',
    dokuSecretKey: '',
    dokuEnvironment: 'sandbox',
    dokuNotificationUrl: '',
  },
  billing: {
    enabled: false,
    invoiceGeneration: false,
    automaticBilling: false,
    billingCycle: 'monthly',
    gracePeriodDays: 7,
    reminderDays: [7, 3, 1],
  },
  subscriptions: {
    enabled: false,
    allowFreePlan: true,
    allowUpgrades: false,
    allowDowngrades: false,
    prorationEnabled: false,
    trialPeriodDays: 14,
    cancelationPolicy: 'end_of_period',
  },
  marketplace: {
    enabled: true,
    paidNodesEnabled: false,
    freeNodesEnabled: true,
    nodeApprovalRequired: true,
    allowNodeUploads: true,
    maxNodeSize: 10,
    allowedFileTypes: ['.js', '.ts', '.json'],
    featuredNodesEnabled: true,
    reviewSystemEnabled: true,
    ratingSystemEnabled: true,
  },
  developer: {
    enabled: true,
    nodePublishingEnabled: true,
    analyticsEnabled: true,
    revenueShareEnabled: false,
    maxNodesPerDeveloper: 50,
    approvalProcessEnabled: true,
    sandboxTestingEnabled: true,
  },
  workflows: {
    // Basic Settings
    enabled: true,
    maxWorkflowsPerUser: 100,
    maxNodesPerWorkflow: 50,
    sharingEnabled: true,
    exportEnabled: true,
    importEnabled: true,

    // Execution Engine Settings
    executionTimeoutMinutes: 30,
    maxConcurrentExecutions: 5,
    defaultExecutionMode: 'optimized',
    retryAttempts: 3,
    continueOnError: false,
    debugModeEnabled: false,

    // Scheduling Settings
    schedulingEnabled: true,
    maxScheduledWorkflows: 20,
    schedulingIntervalMinutes: 1,

    // Performance Settings
    maxConcurrentNodes: 10,
    nodeExecutionTimeoutSeconds: 300,
    memoryLimitMB: 512,
    cpuLimitPercent: 80,

    // Storage & Logging
    executionHistoryRetentionDays: 30,
    logLevel: 'info',
    maxLogSizeMB: 100,
    enableExecutionMetrics: true,

    // Security Settings
    sandboxEnabled: true,
    allowExternalConnections: true,
    allowFileSystemAccess: false,
    allowNetworkAccess: true,

    // Advanced Features
    webhooksEnabled: true,
    apiIntegrationEnabled: true,
    customNodeUploadEnabled: true,
    workflowTemplatesEnabled: true,
  },
  email: {
    enabled: false,
    provider: 'disabled',
    verificationEnabled: false,
    notificationsEnabled: false,
    marketingEmailsEnabled: false,
    fromEmail: '',
    fromName: '',
  },
  security: {
    twoFactorEnabled: false,
    passwordRequirements: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: false,
    },
    sessionTimeoutMinutes: 480,
    maxLoginAttempts: 5,
    lockoutDurationMinutes: 15,
  },
  api: {
    rateLimitEnabled: true,
    requestsPerMinute: 100,
    requestsPerHour: 1000,
    apiKeysEnabled: false,
    webhooksEnabled: false,
    corsEnabled: true,
    allowedOrigins: ['http://localhost:3000'],
  },
  storage: {
    provider: 'local',
    maxFileSize: 50,
    allowedFileTypes: ['.js', '.ts', '.json', '.md', '.txt'],
    compressionEnabled: true,
    backupEnabled: false,
    retentionDays: 90,
  },
  analytics: {
    enabled: true,
    trackingEnabled: true,
    dataRetentionDays: 365,
    anonymizeData: true,
    exportEnabled: true,
    realtimeEnabled: true,
  },
  maintenance: {
    maintenanceMode: false,
    maintenanceMessage: 'System is under maintenance. Please try again later.',
    allowedIPs: [],
    scheduledMaintenance: null,
  },
  features: {
    betaFeaturesEnabled: false,
    experimentalFeaturesEnabled: false,
    debugModeEnabled: false,
    performanceMonitoringEnabled: true,
  },
  ui: {
    darkModeEnabled: true,
    compactModeEnabled: false,
    animationsEnabled: true,
    notificationsEnabled: true,
    soundEnabled: false,
    language: 'en',
    timezone: 'UTC',
  },
};

function getValueType(value) {
  if (typeof value === 'boolean') return 'boolean';
  if (typeof value === 'number') return 'number';
  if (Array.isArray(value)) return 'array';
  if (typeof value === 'object') return 'object';
  return 'string';
}

function stringifyValue(value) {
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  return String(value);
}

async function fixSettings() {
  console.log('🔧 Fixing application settings...');

  try {
    // Get all existing settings
    const existingSettings = await prisma.appSettings.findMany();
    const existingMap = new Map();

    existingSettings.forEach(setting => {
      const key = `${setting.category}.${setting.key}`;
      existingMap.set(key, setting);
    });

    let created = 0;
    let updated = 0;

    // Process each category
    for (const [category, categorySettings] of Object.entries(defaultSettings)) {
      console.log(`📂 Processing ${category} settings...`);

      for (const [key, value] of Object.entries(categorySettings)) {
        const settingKey = `${category}.${key}`;
        const existing = existingMap.get(settingKey);

        const settingData = {
          category,
          key,
          value: stringifyValue(value),
          type: getValueType(value),
          description: `${category} ${key} setting`,
          isSystem: true,
        };

        if (existing) {
          // Update if type or value is different
          if (existing.type !== settingData.type) {
            await prisma.appSettings.update({
              where: { id: existing.id },
              data: {
                type: settingData.type,
                value: settingData.value,
                updatedAt: new Date(),
              },
            });
            updated++;
            console.log(`  ✅ Updated ${settingKey} (type: ${existing.type} → ${settingData.type})`);
          }
        } else {
          // Create new setting
          await prisma.appSettings.create({
            data: {
              id: `${category}_${key}`,
              ...settingData,
            },
          });
          created++;
          console.log(`  ➕ Created ${settingKey}`);
        }
      }
    }

    console.log(`\n✅ Settings fix completed!`);
    console.log(`   📊 Created: ${created} settings`);
    console.log(`   🔄 Updated: ${updated} settings`);

  } catch (error) {
    console.error('❌ Error fixing settings:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixSettings().catch(console.error);
