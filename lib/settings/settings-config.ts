import { Setting<PERSON><PERSON><PERSON><PERSON>, Setting<PERSON>ield } from './types';
import {
  CreditCard,
  Receipt,
  Crown,
  Store,
  Code,
  Workflow,
  Mail,
  Shield,
  Zap,
  Database,
  BarChart3,
  Settings,
  Palette,
  Flag
} from 'lucide-react';

export const settingCategories: SettingCategory[] = [
  {
    id: 'payments',
    name: 'Payment Gateway',
    description: 'Configure payment processing and gateway settings',
    icon: 'CreditCard',
    order: 1
  },
  {
    id: 'billing',
    name: 'Billing System',
    description: 'Manage billing, invoicing, and payment cycles',
    icon: 'Receipt',
    order: 2
  },
  {
    id: 'subscriptions',
    name: 'Subscriptions',
    description: 'Control subscription plans and user access',
    icon: 'Crown',
    order: 3
  },
  {
    id: 'marketplace',
    name: 'Marketplace',
    description: 'Configure node marketplace and paid content',
    icon: 'Store',
    order: 4
  },
  {
    id: 'developer',
    name: 'Developer Tools',
    description: 'Settings for node publishing and developer features',
    icon: 'Code',
    order: 5
  },
  {
    id: 'workflows',
    name: 'Workflow Engine',
    description: 'Configure workflow execution, performance, security, and advanced features',
    icon: 'Workflow',
    order: 6
  },
  {
    id: 'email',
    name: 'Email System',
    description: 'Email providers and notification settings',
    icon: 'Mail',
    order: 7
  },
  {
    id: 'security',
    name: 'Security',
    description: 'Authentication and security policies',
    icon: 'Shield',
    order: 8
  },
  {
    id: 'api',
    name: 'API Settings',
    description: 'Rate limiting and API configuration',
    icon: 'Zap',
    order: 9
  },
  {
    id: 'storage',
    name: 'File Storage',
    description: 'File upload, cloud storage, and backup settings',
    icon: 'HardDrive',
    order: 10
  },
  {
    id: 'database',
    name: 'Database',
    description: 'Database connection, performance, and backup settings',
    icon: 'Database',
    order: 11
  },
  {
    id: 'analytics',
    name: 'Analytics',
    description: 'Data tracking and analytics configuration',
    icon: 'BarChart3',
    order: 12
  },
  {
    id: 'maintenance',
    name: 'Maintenance',
    description: 'System maintenance and monitoring',
    icon: 'Settings',
    order: 13
  },
  {
    id: 'features',
    name: 'Feature Flags',
    description: 'Enable or disable experimental features',
    icon: 'Flag',
    order: 14
  },
  {
    id: 'ui',
    name: 'UI/UX',
    description: 'User interface and experience settings',
    icon: 'Palette',
    order: 15
  }
];

export const settingFields: Record<string, SettingField[]> = {
  payments: [
    {
      key: 'enabled',
      name: 'Enable Payments',
      description: 'Enable payment processing throughout the application',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'provider',
      name: 'Payment Provider',
      description: 'Select the payment gateway provider',
      type: 'select',
      defaultValue: 'disabled',
      options: [
        { label: 'Disabled', value: 'disabled' },
        { label: 'Doku', value: 'doku' }
      ],
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'dokuEnabled',
      name: 'Enable Doku',
      description: 'Enable Doku payment processing',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'provider', value: 'doku' }]
    },
    {
      key: 'testMode',
      name: 'Test Mode',
      description: 'Use test/sandbox mode for payments',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'currency',
      name: 'Default Currency',
      description: 'Default currency for transactions',
      type: 'select',
      defaultValue: 'idr',
      options: [
        { label: 'IDR', value: 'idr' },
        { label: 'USD', value: 'usd' },
        { label: 'EUR', value: 'eur' },
        { label: 'GBP', value: 'gbp' }
      ]
    },
    {
      key: 'commissionRate',
      name: 'Commission Rate',
      description: 'Platform commission rate (0-1)',
      type: 'number',
      defaultValue: 0.25,
      validation: { min: 0, max: 1 }
    },
    {
      key: 'dokuClientId',
      name: 'Doku Client ID',
      description: 'Doku Client ID from your Doku dashboard (e.g., MCH-0001-... or BRN-0297-...)',
      type: 'string',
      defaultValue: '',
      placeholder: 'MCH-0001-... or BRN-0297-...',
      validation: {
        required: true,
        pattern: /^(MCH|BRN)-\d{4}-/,
        patternMessage: 'Client ID should start with MCH-XXXX- (merchant) or BRN-XXXX- (business)'
      },
      dependencies: [{ field: 'provider', value: 'doku' }]
    },
    {
      key: 'dokuSecretKey',
      name: 'Doku Secret Key',
      description: 'Doku Secret Key from your Doku dashboard',
      type: 'password',
      defaultValue: '',
      placeholder: 'Enter your Doku secret key',
      validation: {
        required: true,
        minLength: 10
      },
      dependencies: [{ field: 'provider', value: 'doku' }]
    },
    {
      key: 'dokuEnvironment',
      name: 'Doku Environment',
      description: 'Select Doku environment (sandbox for testing)',
      type: 'select',
      defaultValue: 'sandbox',
      options: [
        { label: 'Sandbox (Testing)', value: 'sandbox' },
        { label: 'Production', value: 'production' }
      ],
      dependencies: [{ field: 'provider', value: 'doku' }]
    },
    {
      key: 'dokuNotificationUrl',
      name: 'Doku Notification URL',
      description: 'URL to receive payment notifications from Doku (webhook endpoint)',
      type: 'string',
      defaultValue: '',
      placeholder: 'https://yourdomain.com/api/payments/webhooks',
      dependencies: [{ field: 'provider', value: 'doku' }]
    }
  ],

  billing: [
    {
      key: 'enabled',
      name: 'Enable Billing',
      description: 'Enable the billing system',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'invoiceGeneration',
      name: 'Generate Invoices',
      description: 'Automatically generate invoices',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'automaticBilling',
      name: 'Automatic Billing',
      description: 'Enable automatic recurring billing',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'billingCycle',
      name: 'Billing Cycle',
      description: 'Default billing cycle',
      type: 'select',
      defaultValue: 'monthly',
      options: [
        { label: 'Monthly', value: 'monthly' },
        { label: 'Yearly', value: 'yearly' }
      ]
    },
    {
      key: 'gracePeriodDays',
      name: 'Grace Period (Days)',
      description: 'Grace period before service suspension',
      type: 'number',
      defaultValue: 7,
      validation: { min: 0, max: 30 }
    }
  ],

  subscriptions: [
    {
      key: 'enabled',
      name: 'Enable Subscriptions',
      description: 'Enable subscription system',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'allowFreePlan',
      name: 'Allow Free Plan',
      description: 'Allow users to use free plan',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'allowUpgrades',
      name: 'Allow Upgrades',
      description: 'Allow users to upgrade their plans',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'allowDowngrades',
      name: 'Allow Downgrades',
      description: 'Allow users to downgrade their plans',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'trialPeriodDays',
      name: 'Trial Period (Days)',
      description: 'Free trial period in days',
      type: 'number',
      defaultValue: 14,
      validation: { min: 0, max: 90 }
    }
  ],

  marketplace: [
    {
      key: 'enabled',
      name: 'Enable Marketplace',
      description: 'Enable the node marketplace',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'paidNodesEnabled',
      name: 'Enable Paid Nodes',
      description: 'Allow paid nodes in marketplace',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'freeNodesEnabled',
      name: 'Enable Free Nodes',
      description: 'Allow free nodes in marketplace',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'nodeApprovalRequired',
      name: 'Require Node Approval',
      description: 'Require admin approval for new nodes',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'allowNodeUploads',
      name: 'Allow Node Uploads',
      description: 'Allow developers to upload nodes',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'maxNodeSize',
      name: 'Max Node Size (MB)',
      description: 'Maximum size for uploaded nodes',
      type: 'number',
      defaultValue: 10,
      validation: { min: 1, max: 100 }
    },
    {
      key: 'reviewSystemEnabled',
      name: 'Enable Reviews',
      description: 'Enable node review system',
      type: 'boolean',
      defaultValue: true
    }
  ],

  developer: [
    {
      key: 'enabled',
      name: 'Enable Developer Tools',
      description: 'Enable developer features',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'nodePublishingEnabled',
      name: 'Enable Node Publishing',
      description: 'Allow developers to publish nodes',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'analyticsEnabled',
      name: 'Enable Analytics',
      description: 'Enable developer analytics',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'revenueShareEnabled',
      name: 'Enable Revenue Sharing',
      description: 'Enable revenue sharing for developers',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'maxNodesPerDeveloper',
      name: 'Max Nodes per Developer',
      description: 'Maximum nodes a developer can publish',
      type: 'number',
      defaultValue: 50,
      validation: { min: 1, max: 1000 }
    }
  ],

  workflows: [
    // Basic Settings Section
    {
      key: 'enabled',
      name: 'Enable Workflow Engine',
      description: 'Enable the workflow execution engine',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'maxWorkflowsPerUser',
      name: 'Max Workflows per User',
      description: 'Maximum number of workflows a user can create',
      type: 'number',
      defaultValue: 100,
      validation: { min: 1, max: 1000 }
    },
    {
      key: 'maxNodesPerWorkflow',
      name: 'Max Nodes per Workflow',
      description: 'Maximum number of nodes allowed in a single workflow',
      type: 'number',
      defaultValue: 50,
      validation: { min: 1, max: 500 }
    },
    {
      key: 'sharingEnabled',
      name: 'Enable Workflow Sharing',
      description: 'Allow users to share workflows with others',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'exportEnabled',
      name: 'Enable Workflow Export',
      description: 'Allow users to export workflows',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'importEnabled',
      name: 'Enable Workflow Import',
      description: 'Allow users to import workflows',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },

    // Execution Engine Settings Section
    {
      key: 'executionTimeoutMinutes',
      name: 'Execution Timeout (Minutes)',
      description: 'Maximum time a workflow can run before timing out',
      type: 'number',
      defaultValue: 30,
      validation: { min: 1, max: 1440 } // 1 minute to 24 hours
    },
    {
      key: 'maxConcurrentExecutions',
      name: 'Max Concurrent Executions',
      description: 'Maximum number of workflows that can run simultaneously',
      type: 'number',
      defaultValue: 5,
      validation: { min: 1, max: 50 }
    },
    {
      key: 'defaultExecutionMode',
      name: 'Default Execution Mode',
      description: 'Default mode for workflow execution',
      type: 'select',
      defaultValue: 'optimized',
      options: [
        { label: 'Sequential', value: 'sequential' },
        { label: 'Parallel', value: 'parallel' },
        { label: 'Optimized', value: 'optimized' }
      ]
    },
    {
      key: 'retryAttempts',
      name: 'Retry Attempts',
      description: 'Number of times to retry failed node executions',
      type: 'number',
      defaultValue: 3,
      validation: { min: 0, max: 10 }
    },
    {
      key: 'continueOnError',
      name: 'Continue on Error',
      description: 'Continue workflow execution even if some nodes fail',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'debugModeEnabled',
      name: 'Enable Debug Mode',
      description: 'Enable detailed debugging information during execution',
      type: 'boolean',
      defaultValue: false
    },

    // Scheduling Settings Section
    {
      key: 'schedulingEnabled',
      name: 'Enable Scheduling',
      description: 'Allow workflows to be scheduled for automatic execution',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'maxScheduledWorkflows',
      name: 'Max Scheduled Workflows',
      description: 'Maximum number of workflows that can be scheduled per user',
      type: 'number',
      defaultValue: 20,
      validation: { min: 1, max: 100 },
      dependencies: [{ field: 'schedulingEnabled', value: true }]
    },
    {
      key: 'schedulingIntervalMinutes',
      name: 'Minimum Scheduling Interval (Minutes)',
      description: 'Minimum interval between scheduled executions',
      type: 'number',
      defaultValue: 1,
      validation: { min: 1, max: 1440 },
      dependencies: [{ field: 'schedulingEnabled', value: true }]
    },

    // Performance Settings Section
    {
      key: 'maxConcurrentNodes',
      name: 'Max Concurrent Nodes',
      description: 'Maximum number of nodes that can execute simultaneously in a workflow',
      type: 'number',
      defaultValue: 10,
      validation: { min: 1, max: 100 }
    },
    {
      key: 'nodeExecutionTimeoutSeconds',
      name: 'Node Execution Timeout (Seconds)',
      description: 'Maximum time a single node can run before timing out',
      type: 'number',
      defaultValue: 300,
      validation: { min: 1, max: 3600 } // 1 second to 1 hour
    },
    {
      key: 'memoryLimitMB',
      name: 'Memory Limit (MB)',
      description: 'Maximum memory usage per workflow execution',
      type: 'number',
      defaultValue: 512,
      validation: { min: 64, max: 8192 }
    },
    {
      key: 'cpuLimitPercent',
      name: 'CPU Limit (%)',
      description: 'Maximum CPU usage percentage per workflow execution',
      type: 'number',
      defaultValue: 80,
      validation: { min: 10, max: 100 }
    },

    // Storage & Logging Section
    {
      key: 'executionHistoryRetentionDays',
      name: 'Execution History Retention (Days)',
      description: 'Number of days to keep workflow execution history',
      type: 'number',
      defaultValue: 30,
      validation: { min: 1, max: 365 }
    },
    {
      key: 'logLevel',
      name: 'Log Level',
      description: 'Minimum log level for workflow execution logs',
      type: 'select',
      defaultValue: 'info',
      options: [
        { label: 'Error', value: 'error' },
        { label: 'Warning', value: 'warn' },
        { label: 'Info', value: 'info' },
        { label: 'Debug', value: 'debug' }
      ]
    },
    {
      key: 'maxLogSizeMB',
      name: 'Max Log Size (MB)',
      description: 'Maximum size of log files per workflow execution',
      type: 'number',
      defaultValue: 100,
      validation: { min: 1, max: 1000 }
    },
    {
      key: 'enableExecutionMetrics',
      name: 'Enable Execution Metrics',
      description: 'Collect and store detailed execution metrics',
      type: 'boolean',
      defaultValue: true
    },

    // Security Settings Section
    {
      key: 'sandboxEnabled',
      name: 'Enable Sandbox',
      description: 'Run workflow nodes in a secure sandbox environment',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'allowExternalConnections',
      name: 'Allow External Connections',
      description: 'Allow workflow nodes to make external HTTP requests',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'allowFileSystemAccess',
      name: 'Allow File System Access',
      description: 'Allow workflow nodes to access the file system',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'allowNetworkAccess',
      name: 'Allow Network Access',
      description: 'Allow workflow nodes to access network resources',
      type: 'boolean',
      defaultValue: true
    },

    // Advanced Features Section
    {
      key: 'webhooksEnabled',
      name: 'Enable Webhooks',
      description: 'Allow workflows to trigger and receive webhooks',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'apiIntegrationEnabled',
      name: 'Enable API Integration',
      description: 'Allow workflows to integrate with external APIs',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'customNodeUploadEnabled',
      name: 'Enable Custom Node Upload',
      description: 'Allow users to upload custom workflow nodes',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'workflowTemplatesEnabled',
      name: 'Enable Workflow Templates',
      description: 'Allow users to create and use workflow templates',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    }
  ],

  storage: [
    // Provider Configuration Section
    {
      key: 'enabled',
      name: 'Enable File Storage',
      description: 'Enable file upload and storage functionality',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'provider',
      name: 'Storage Provider',
      description: 'Choose your file storage provider',
      type: 'select',
      defaultValue: 'local',
      options: [
        { label: 'Local Storage', value: 'local' },
        { label: 'Amazon S3', value: 'aws' },
        { label: 'Google Cloud Storage', value: 'gcp' },
        { label: 'Azure Blob Storage', value: 'azure' },
        { label: 'DigitalOcean Spaces', value: 'digitalocean' },
        { label: 'Cloudinary', value: 'cloudinary' }
      ],
      dependencies: [{ field: 'enabled', value: true }]
    },

    // File Upload Settings Section
    {
      key: 'maxFileSize',
      name: 'Max File Size (MB)',
      description: 'Maximum file size allowed for uploads',
      type: 'number',
      defaultValue: 50,
      validation: { min: 1, max: 1000 },
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'maxTotalStorage',
      name: 'Max Total Storage (GB)',
      description: 'Maximum total storage space allowed',
      type: 'number',
      defaultValue: 10,
      validation: { min: 1, max: 1000 },
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'allowedFileTypes',
      name: 'Allowed File Types',
      description: 'File extensions that are allowed for upload',
      type: 'array',
      defaultValue: ['.js', '.ts', '.json', '.md', '.txt', '.pdf', '.png', '.jpg', '.jpeg'],
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'blockedFileTypes',
      name: 'Blocked File Types',
      description: 'File extensions that are blocked from upload',
      type: 'array',
      defaultValue: ['.exe', '.bat', '.cmd', '.scr'],
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'allowExecutableFiles',
      name: 'Allow Executable Files',
      description: 'Allow uploading of executable files (security risk)',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },

    // Storage Optimization Section
    {
      key: 'compressionEnabled',
      name: 'Enable Compression',
      description: 'Compress files to save storage space',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'compressionQuality',
      name: 'Compression Quality (%)',
      description: 'Quality level for image compression (1-100)',
      type: 'number',
      defaultValue: 85,
      validation: { min: 1, max: 100 },
      dependencies: [{ field: 'compressionEnabled', value: true }]
    },
    {
      key: 'autoOptimizeImages',
      name: 'Auto-Optimize Images',
      description: 'Automatically optimize images for web',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'generateThumbnails',
      name: 'Generate Thumbnails',
      description: 'Automatically generate thumbnails for images',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },

    // Backup & Retention Section
    {
      key: 'backupEnabled',
      name: 'Enable Backups',
      description: 'Create regular backups of uploaded files',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'backupFrequency',
      name: 'Backup Frequency',
      description: 'How often to create backups',
      type: 'select',
      defaultValue: 'daily',
      options: [
        { label: 'Daily', value: 'daily' },
        { label: 'Weekly', value: 'weekly' },
        { label: 'Monthly', value: 'monthly' }
      ],
      dependencies: [{ field: 'backupEnabled', value: true }]
    },
    {
      key: 'retentionDays',
      name: 'File Retention (Days)',
      description: 'Number of days to keep files before auto-deletion',
      type: 'number',
      defaultValue: 90,
      validation: { min: 1, max: 3650 },
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'autoCleanupEnabled',
      name: 'Enable Auto-Cleanup',
      description: 'Automatically delete old files based on retention policy',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },

    // Security Section
    {
      key: 'virusScanEnabled',
      name: 'Enable Virus Scanning',
      description: 'Scan uploaded files for viruses and malware',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'encryptionEnabled',
      name: 'Enable File Encryption',
      description: 'Encrypt files at rest for security',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'accessLoggingEnabled',
      name: 'Enable Access Logging',
      description: 'Log all file access and download activities',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'enabled', value: true }]
    },
    {
      key: 'publicAccessEnabled',
      name: 'Allow Public Access',
      description: 'Allow public access to uploaded files via direct URLs',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'enabled', value: true }]
    },

    // AWS S3 Settings Section
    {
      key: 'awsRegion',
      name: 'AWS Region',
      description: 'AWS region for S3 bucket',
      type: 'select',
      defaultValue: 'us-east-1',
      options: [
        { label: 'US East (N. Virginia)', value: 'us-east-1' },
        { label: 'US East (Ohio)', value: 'us-east-2' },
        { label: 'US West (Oregon)', value: 'us-west-2' },
        { label: 'EU (Ireland)', value: 'eu-west-1' },
        { label: 'Asia Pacific (Singapore)', value: 'ap-southeast-1' }
      ],
      dependencies: [{ field: 'provider', value: 'aws' }]
    },
    {
      key: 'awsBucket',
      name: 'S3 Bucket Name',
      description: 'Name of your S3 bucket',
      type: 'string',
      defaultValue: '',
      validation: { required: true },
      dependencies: [{ field: 'provider', value: 'aws' }]
    },
    {
      key: 'awsAccessKeyId',
      name: 'AWS Access Key ID',
      description: 'Your AWS access key ID',
      type: 'string',
      defaultValue: '',
      validation: { required: true },
      dependencies: [{ field: 'provider', value: 'aws' }]
    },
    {
      key: 'awsSecretAccessKey',
      name: 'AWS Secret Access Key',
      description: 'Your AWS secret access key',
      type: 'string',
      defaultValue: '',
      validation: { required: true },
      dependencies: [{ field: 'provider', value: 'aws' }]
    },
    {
      key: 'awsCloudFrontEnabled',
      name: 'Enable CloudFront CDN',
      description: 'Use CloudFront CDN for faster file delivery',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'provider', value: 'aws' }]
    },
    {
      key: 'awsCloudFrontDomain',
      name: 'CloudFront Domain',
      description: 'Your CloudFront distribution domain',
      type: 'string',
      defaultValue: '',
      dependencies: [{ field: 'awsCloudFrontEnabled', value: true }]
    }
  ],

  database: [
    // Connection Settings Section
    {
      key: 'provider',
      name: 'Database Provider',
      description: 'Choose your database provider',
      type: 'select',
      defaultValue: 'sqlite',
      options: [
        { label: 'SQLite', value: 'sqlite' },
        { label: 'PostgreSQL', value: 'postgresql' },
        { label: 'MySQL', value: 'mysql' },
        { label: 'MongoDB', value: 'mongodb' }
      ]
    },
    {
      key: 'host',
      name: 'Database Host',
      description: 'Database server hostname or IP address',
      type: 'string',
      defaultValue: 'localhost',
      validation: { required: true },
      dependencies: [{ field: 'provider', value: 'postgresql' }]
    },
    {
      key: 'port',
      name: 'Database Port',
      description: 'Database server port number',
      type: 'number',
      defaultValue: 5432,
      validation: { min: 1, max: 65535 },
      dependencies: [{ field: 'provider', value: 'postgresql' }]
    },
    {
      key: 'database',
      name: 'Database Name',
      description: 'Name of the database to connect to',
      type: 'string',
      defaultValue: 'nextjs_app',
      validation: { required: true },
      dependencies: [{ field: 'provider', value: 'postgresql' }]
    },
    {
      key: 'username',
      name: 'Database Username',
      description: 'Username for database authentication',
      type: 'string',
      defaultValue: '',
      validation: { required: true },
      dependencies: [{ field: 'provider', value: 'postgresql' }]
    },
    {
      key: 'password',
      name: 'Database Password',
      description: 'Password for database authentication',
      type: 'string',
      defaultValue: '',
      validation: { required: true },
      dependencies: [{ field: 'provider', value: 'postgresql' }]
    },
    {
      key: 'ssl',
      name: 'Enable SSL',
      description: 'Use SSL/TLS for database connections',
      type: 'boolean',
      defaultValue: false,
      dependencies: [{ field: 'provider', value: 'postgresql' }]
    },

    // Connection Pool Settings Section
    {
      key: 'maxConnections',
      name: 'Max Connections',
      description: 'Maximum number of database connections in the pool',
      type: 'number',
      defaultValue: 10,
      validation: { min: 1, max: 100 }
    },
    {
      key: 'minConnections',
      name: 'Min Connections',
      description: 'Minimum number of database connections to maintain',
      type: 'number',
      defaultValue: 2,
      validation: { min: 1, max: 50 }
    },
    {
      key: 'connectionTimeout',
      name: 'Connection Timeout (seconds)',
      description: 'Maximum time to wait for a database connection',
      type: 'number',
      defaultValue: 30,
      validation: { min: 1, max: 300 }
    },
    {
      key: 'idleTimeout',
      name: 'Idle Timeout (seconds)',
      description: 'Time before idle connections are closed',
      type: 'number',
      defaultValue: 600,
      validation: { min: 60, max: 3600 }
    },

    // Performance Settings Section
    {
      key: 'queryTimeout',
      name: 'Query Timeout (seconds)',
      description: 'Maximum time for a single query to execute',
      type: 'number',
      defaultValue: 30,
      validation: { min: 1, max: 300 }
    },
    {
      key: 'slowQueryThreshold',
      name: 'Slow Query Threshold (ms)',
      description: 'Log queries that take longer than this threshold',
      type: 'number',
      defaultValue: 1000,
      validation: { min: 100, max: 10000 }
    },
    {
      key: 'enableQueryLogging',
      name: 'Enable Query Logging',
      description: 'Log all database queries for debugging',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'enableSlowQueryLogging',
      name: 'Enable Slow Query Logging',
      description: 'Log queries that exceed the slow query threshold',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'cacheEnabled',
      name: 'Enable Query Cache',
      description: 'Cache frequently used query results',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'cacheTTL',
      name: 'Cache TTL (seconds)',
      description: 'Time-to-live for cached query results',
      type: 'number',
      defaultValue: 300,
      validation: { min: 60, max: 3600 },
      dependencies: [{ field: 'cacheEnabled', value: true }]
    },

    // Backup Settings Section
    {
      key: 'backupEnabled',
      name: 'Enable Database Backups',
      description: 'Create regular database backups',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'backupFrequency',
      name: 'Backup Frequency',
      description: 'How often to create database backups',
      type: 'select',
      defaultValue: 'daily',
      options: [
        { label: 'Hourly', value: 'hourly' },
        { label: 'Daily', value: 'daily' },
        { label: 'Weekly', value: 'weekly' }
      ],
      dependencies: [{ field: 'backupEnabled', value: true }]
    },
    {
      key: 'backupRetentionDays',
      name: 'Backup Retention (Days)',
      description: 'Number of days to keep database backups',
      type: 'number',
      defaultValue: 30,
      validation: { min: 1, max: 365 },
      dependencies: [{ field: 'backupEnabled', value: true }]
    },
    {
      key: 'backupCompression',
      name: 'Enable Backup Compression',
      description: 'Compress backup files to save storage space',
      type: 'boolean',
      defaultValue: true,
      dependencies: [{ field: 'backupEnabled', value: true }]
    },

    // Monitoring Settings Section
    {
      key: 'performanceMonitoringEnabled',
      name: 'Enable Performance Monitoring',
      description: 'Monitor database performance metrics',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'alertsEnabled',
      name: 'Enable Alerts',
      description: 'Send alerts for database issues',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'diskSpaceThreshold',
      name: 'Disk Space Alert Threshold (%)',
      description: 'Alert when disk space usage exceeds this percentage',
      type: 'number',
      defaultValue: 85,
      validation: { min: 50, max: 95 },
      dependencies: [{ field: 'alertsEnabled', value: true }]
    },
    {
      key: 'connectionThreshold',
      name: 'Connection Alert Threshold (%)',
      description: 'Alert when connection pool usage exceeds this percentage',
      type: 'number',
      defaultValue: 80,
      validation: { min: 50, max: 95 },
      dependencies: [{ field: 'alertsEnabled', value: true }]
    }
  ],

  storage: [
    // Basic Settings
    {
      key: 'enabled',
      name: 'Enable File Storage',
      description: 'Enable file upload and storage functionality',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'provider',
      name: 'Storage Provider',
      description: 'Choose your file storage provider',
      type: 'select',
      defaultValue: 'local',
      options: [
        { label: 'Local Storage', value: 'local' },
        { label: 'Amazon S3', value: 'aws' },
        { label: 'Google Cloud Storage', value: 'gcp' },
        { label: 'Azure Blob Storage', value: 'azure' },
        { label: 'DigitalOcean Spaces', value: 'digitalocean' },
        { label: 'Cloudinary', value: 'cloudinary' }
      ]
    },
    {
      key: 'maxFileSize',
      name: 'Max File Size (MB)',
      description: 'Maximum file size allowed for uploads',
      type: 'number',
      defaultValue: 50,
      validation: { min: 1, max: 1000 }
    },
    {
      key: 'maxTotalStorage',
      name: 'Max Total Storage (GB)',
      description: 'Maximum total storage space allowed',
      type: 'number',
      defaultValue: 10,
      validation: { min: 1, max: 1000 }
    },
    {
      key: 'compressionEnabled',
      name: 'Enable Compression',
      description: 'Compress files to save storage space',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'compressionQuality',
      name: 'Compression Quality (%)',
      description: 'Quality level for image compression (1-100)',
      type: 'number',
      defaultValue: 85,
      validation: { min: 1, max: 100 }
    },
    {
      key: 'autoOptimizeImages',
      name: 'Auto-Optimize Images',
      description: 'Automatically optimize images for web',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'generateThumbnails',
      name: 'Generate Thumbnails',
      description: 'Automatically generate thumbnails for images',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'backupEnabled',
      name: 'Enable Backups',
      description: 'Create regular backups of uploaded files',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'backupFrequency',
      name: 'Backup Frequency',
      description: 'How often to create backups',
      type: 'select',
      defaultValue: 'daily',
      options: [
        { label: 'Daily', value: 'daily' },
        { label: 'Weekly', value: 'weekly' },
        { label: 'Monthly', value: 'monthly' }
      ]
    },
    {
      key: 'retentionDays',
      name: 'File Retention (Days)',
      description: 'Number of days to keep files before auto-deletion',
      type: 'number',
      defaultValue: 90,
      validation: { min: 1, max: 3650 }
    },
    {
      key: 'encryptionEnabled',
      name: 'Enable File Encryption',
      description: 'Encrypt files at rest for security',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'accessLoggingEnabled',
      name: 'Enable Access Logging',
      description: 'Log all file access and download activities',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'publicAccessEnabled',
      name: 'Allow Public Access',
      description: 'Allow public access to uploaded files via direct URLs',
      type: 'boolean',
      defaultValue: false
    }
  ],

  database: [
    {
      key: 'provider',
      name: 'Database Provider',
      description: 'Choose your database provider',
      type: 'select',
      defaultValue: 'sqlite',
      options: [
        { label: 'SQLite', value: 'sqlite' },
        { label: 'PostgreSQL', value: 'postgresql' },
        { label: 'MySQL', value: 'mysql' },
        { label: 'MongoDB', value: 'mongodb' }
      ]
    },
    {
      key: 'host',
      name: 'Database Host',
      description: 'Database server hostname or IP address',
      type: 'string',
      defaultValue: 'localhost'
    },
    {
      key: 'port',
      name: 'Database Port',
      description: 'Database server port number',
      type: 'number',
      defaultValue: 5432,
      validation: { min: 1, max: 65535 }
    },
    {
      key: 'database',
      name: 'Database Name',
      description: 'Name of the database to connect to',
      type: 'string',
      defaultValue: 'nextjs_app'
    },
    {
      key: 'maxConnections',
      name: 'Max Connections',
      description: 'Maximum number of database connections in the pool',
      type: 'number',
      defaultValue: 10,
      validation: { min: 1, max: 100 }
    },
    {
      key: 'connectionTimeout',
      name: 'Connection Timeout (seconds)',
      description: 'Maximum time to wait for a database connection',
      type: 'number',
      defaultValue: 30,
      validation: { min: 1, max: 300 }
    },
    {
      key: 'queryTimeout',
      name: 'Query Timeout (seconds)',
      description: 'Maximum time for a single query to execute',
      type: 'number',
      defaultValue: 30,
      validation: { min: 1, max: 300 }
    },
    {
      key: 'slowQueryThreshold',
      name: 'Slow Query Threshold (ms)',
      description: 'Log queries that take longer than this threshold',
      type: 'number',
      defaultValue: 1000,
      validation: { min: 100, max: 10000 }
    },
    {
      key: 'enableQueryLogging',
      name: 'Enable Query Logging',
      description: 'Log all database queries for debugging',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'enableSlowQueryLogging',
      name: 'Enable Slow Query Logging',
      description: 'Log queries that exceed the slow query threshold',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'cacheEnabled',
      name: 'Enable Query Cache',
      description: 'Cache frequently used query results',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'cacheTTL',
      name: 'Cache TTL (seconds)',
      description: 'Time-to-live for cached query results',
      type: 'number',
      defaultValue: 300,
      validation: { min: 60, max: 3600 }
    },
    {
      key: 'backupEnabled',
      name: 'Enable Database Backups',
      description: 'Create regular database backups',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'backupFrequency',
      name: 'Backup Frequency',
      description: 'How often to create database backups',
      type: 'select',
      defaultValue: 'daily',
      options: [
        { label: 'Hourly', value: 'hourly' },
        { label: 'Daily', value: 'daily' },
        { label: 'Weekly', value: 'weekly' }
      ]
    },
    {
      key: 'backupRetentionDays',
      name: 'Backup Retention (Days)',
      description: 'Number of days to keep database backups',
      type: 'number',
      defaultValue: 30,
      validation: { min: 1, max: 365 }
    },
    {
      key: 'performanceMonitoringEnabled',
      name: 'Enable Performance Monitoring',
      description: 'Monitor database performance metrics',
      type: 'boolean',
      defaultValue: true
    },
    {
      key: 'alertsEnabled',
      name: 'Enable Alerts',
      description: 'Send alerts for database issues',
      type: 'boolean',
      defaultValue: false
    },
    {
      key: 'diskSpaceThreshold',
      name: 'Disk Space Alert Threshold (%)',
      description: 'Alert when disk space usage exceeds this percentage',
      type: 'number',
      defaultValue: 85,
      validation: { min: 50, max: 95 }
    }
  ]
};

export function getSettingField(category: string, key: string): SettingField | undefined {
  return settingFields[category]?.find(field => field.key === key);
}

export function getCategoryIcon(category: string): any {
  const iconMap: Record<string, any> = {
    payments: CreditCard,
    billing: Receipt,
    subscriptions: Crown,
    marketplace: Store,
    developer: Code,
    workflows: Workflow,
    email: Mail,
    security: Shield,
    api: Zap,
    storage: Database,
    analytics: BarChart3,
    maintenance: Settings,
    features: Flag,
    ui: Palette
  };

  return iconMap[category] || Settings;
}
