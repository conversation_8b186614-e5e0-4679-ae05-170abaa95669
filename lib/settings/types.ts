// Application Settings Types

export interface AppSettings {
  // Payment Gateway Settings
  payments: {
    enabled: boolean;
    provider: 'doku' | 'disabled';
    dokuEnabled: boolean;
    testMode: boolean;
    currency: string;
    commissionRate: number;
    minimumAmount: number;
    maximumAmount: number;
    // Doku specific settings
    dokuClientId: string;
    dokuSecretKey: string;
    dokuEnvironment: 'sandbox' | 'production';
    dokuNotificationUrl: string;
  };

  // Billing Settings
  billing: {
    enabled: boolean;
    invoiceGeneration: boolean;
    automaticBilling: boolean;
    billingCycle: 'monthly' | 'yearly';
    gracePeriodDays: number;
    reminderDays: number[];
  };

  // Subscription Settings
  subscriptions: {
    enabled: boolean;
    allowFreePlan: boolean;
    allowUpgrades: boolean;
    allowDowngrades: boolean;
    prorationEnabled: boolean;
    trialPeriodDays: number;
    cancelationPolicy: 'immediate' | 'end_of_period';
  };

  // Marketplace Settings
  marketplace: {
    enabled: boolean;
    paidNodesEnabled: boolean;
    freeNodesEnabled: boolean;
    nodeApprovalRequired: boolean;
    allowNodeUploads: boolean;
    maxNodeSize: number; // in MB
    allowedFileTypes: string[];
    featuredNodesEnabled: boolean;
    reviewSystemEnabled: boolean;
    ratingSystemEnabled: boolean;
  };

  // Developer Settings
  developer: {
    enabled: boolean;
    nodePublishingEnabled: boolean;
    analyticsEnabled: boolean;
    revenueShareEnabled: boolean;
    maxNodesPerDeveloper: number;
    approvalProcessEnabled: boolean;
    sandboxTestingEnabled: boolean;
  };

  // Workflow Engine Settings
  workflows: {
    // Basic Settings
    enabled: boolean;
    maxWorkflowsPerUser: number;
    maxNodesPerWorkflow: number;
    sharingEnabled: boolean;
    exportEnabled: boolean;
    importEnabled: boolean;

    // Execution Engine Settings
    executionTimeoutMinutes: number;
    maxConcurrentExecutions: number;
    defaultExecutionMode: 'sequential' | 'parallel' | 'optimized';
    retryAttempts: number;
    continueOnError: boolean;
    debugModeEnabled: boolean;

    // Scheduling Settings
    schedulingEnabled: boolean;
    maxScheduledWorkflows: number;
    schedulingIntervalMinutes: number;

    // Performance Settings
    maxConcurrentNodes: number;
    nodeExecutionTimeoutSeconds: number;
    memoryLimitMB: number;
    cpuLimitPercent: number;

    // Storage & Logging
    executionHistoryRetentionDays: number;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    maxLogSizeMB: number;
    enableExecutionMetrics: boolean;

    // Security Settings
    sandboxEnabled: boolean;
    allowExternalConnections: boolean;
    allowFileSystemAccess: boolean;
    allowNetworkAccess: boolean;

    // Advanced Features
    webhooksEnabled: boolean;
    apiIntegrationEnabled: boolean;
    customNodeUploadEnabled: boolean;
    workflowTemplatesEnabled: boolean;
  };

  // Email System Settings
  email: {
    // Basic Configuration
    enabled: boolean;
    provider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses' | 'postmark' | 'resend' | 'disabled';
    fromEmail: string;
    fromName: string;
    replyToEmail: string;

    // Email Features
    verificationEnabled: boolean;
    notificationsEnabled: boolean;
    marketingEmailsEnabled: boolean;
    transactionalEmailsEnabled: boolean;
    emailTemplatesEnabled: boolean;

    // SMTP Settings
    smtpHost: string;
    smtpPort: number;
    smtpSecure: boolean;
    smtpUsername: string;
    smtpPassword: string;
    smtpTLS: boolean;

    // SendGrid Settings
    sendgridApiKey: string;
    sendgridTemplateId: string;
    sendgridWebhookEnabled: boolean;

    // Mailgun Settings
    mailgunApiKey: string;
    mailgunDomain: string;
    mailgunRegion: 'us' | 'eu';
    mailgunWebhookEnabled: boolean;

    // Amazon SES Settings
    sesAccessKeyId: string;
    sesSecretAccessKey: string;
    sesRegion: string;
    sesConfigurationSet: string;

    // Postmark Settings
    postmarkApiKey: string;
    postmarkTemplateId: string;
    postmarkWebhookEnabled: boolean;

    // Resend Settings
    resendApiKey: string;
    resendWebhookEnabled: boolean;

    // Email Delivery Settings
    maxRetryAttempts: number;
    retryDelayMinutes: number;
    bounceHandlingEnabled: boolean;
    unsubscribeHandlingEnabled: boolean;

    // Rate Limiting
    rateLimitEnabled: boolean;
    maxEmailsPerHour: number;
    maxEmailsPerDay: number;

    // Email Tracking
    trackOpens: boolean;
    trackClicks: boolean;
    trackUnsubscribes: boolean;

    // Email Queue
    queueEnabled: boolean;
    queueMaxSize: number;
    queueProcessingInterval: number; // minutes

    // Email Templates
    welcomeEmailEnabled: boolean;
    passwordResetEmailEnabled: boolean;
    emailVerificationEnabled: boolean;
    notificationEmailEnabled: boolean;

    // Email Security
    dkimEnabled: boolean;
    spfEnabled: boolean;
    dmarcEnabled: boolean;

    // Email Analytics
    analyticsEnabled: boolean;
    retentionDays: number;

    // Testing & Development
    testModeEnabled: boolean;
    testEmailAddress: string;
    logEmailsEnabled: boolean;
  };

  // Security Settings
  security: {
    twoFactorEnabled: boolean;
    passwordRequirements: {
      minLength: number;
      requireUppercase: boolean;
      requireLowercase: boolean;
      requireNumbers: boolean;
      requireSpecialChars: boolean;
    };
    sessionTimeoutMinutes: number;
    maxLoginAttempts: number;
    lockoutDurationMinutes: number;
  };

  // API Settings
  api: {
    rateLimitEnabled: boolean;
    requestsPerMinute: number;
    requestsPerHour: number;
    apiKeysEnabled: boolean;
    webhooksEnabled: boolean;
    corsEnabled: boolean;
    allowedOrigins: string[];
  };

  // File Storage Settings
  storage: {
    // Provider Configuration
    provider: 'local' | 'aws' | 'gcp' | 'azure' | 'cloudinary' | 'digitalocean';
    enabled: boolean;

    // File Upload Settings
    maxFileSize: number; // in MB
    maxTotalStorage: number; // in GB
    allowedFileTypes: string[];
    blockedFileTypes: string[];
    allowExecutableFiles: boolean;

    // Storage Optimization
    compressionEnabled: boolean;
    compressionQuality: number; // 1-100
    autoOptimizeImages: boolean;
    generateThumbnails: boolean;
    thumbnailSizes: number[]; // [150, 300, 600]

    // Backup & Retention
    backupEnabled: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
    retentionDays: number;
    autoCleanupEnabled: boolean;

    // Security
    virusScanEnabled: boolean;
    encryptionEnabled: boolean;
    accessLoggingEnabled: boolean;
    publicAccessEnabled: boolean;

    // AWS S3 Settings
    awsRegion: string;
    awsBucket: string;
    awsAccessKeyId: string;
    awsSecretAccessKey: string;
    awsCloudFrontEnabled: boolean;
    awsCloudFrontDomain: string;

    // Google Cloud Storage Settings
    gcpProjectId: string;
    gcpBucket: string;
    gcpKeyFile: string;
    gcpRegion: string;

    // Azure Blob Storage Settings
    azureAccountName: string;
    azureAccountKey: string;
    azureContainer: string;
    azureRegion: string;

    // DigitalOcean Spaces Settings
    doSpacesKey: string;
    doSpacesSecret: string;
    doSpacesEndpoint: string;
    doSpacesBucket: string;
    doSpacesRegion: string;

    // Cloudinary Settings
    cloudinaryCloudName: string;
    cloudinaryApiKey: string;
    cloudinaryApiSecret: string;
    cloudinaryFolder: string;
  };

  // Database Settings
  database: {
    // Connection Settings
    provider: 'sqlite' | 'postgresql' | 'mysql' | 'mongodb';
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl: boolean;

    // Connection Pool Settings
    maxConnections: number;
    minConnections: number;
    connectionTimeout: number; // seconds
    idleTimeout: number; // seconds

    // Performance Settings
    queryTimeout: number; // seconds
    slowQueryThreshold: number; // milliseconds
    enableQueryLogging: boolean;
    enableSlowQueryLogging: boolean;
    cacheEnabled: boolean;
    cacheTTL: number; // seconds

    // Backup Settings
    backupEnabled: boolean;
    backupFrequency: 'hourly' | 'daily' | 'weekly';
    backupRetentionDays: number;
    backupCompression: boolean;
    backupLocation: 'local' | 'cloud';

    // Maintenance Settings
    autoVacuumEnabled: boolean;
    autoAnalyzeEnabled: boolean;
    maintenanceWindow: string; // "02:00-04:00"
    indexOptimizationEnabled: boolean;

    // Security Settings
    encryptionAtRest: boolean;
    encryptionInTransit: boolean;
    auditLoggingEnabled: boolean;
    accessLoggingEnabled: boolean;

    // Monitoring Settings
    performanceMonitoringEnabled: boolean;
    alertsEnabled: boolean;
    diskSpaceThreshold: number; // percentage
    connectionThreshold: number; // percentage

    // Migration Settings
    autoMigrationsEnabled: boolean;
    migrationTimeout: number; // seconds
    rollbackEnabled: boolean;

    // Replication Settings (for production)
    replicationEnabled: boolean;
    readReplicaEnabled: boolean;
    replicationLag: number; // seconds
  };

  // Analytics Settings
  analytics: {
    enabled: boolean;
    trackingEnabled: boolean;
    dataRetentionDays: number;
    anonymizeData: boolean;
    exportEnabled: boolean;
    realtimeEnabled: boolean;
  };

  // Maintenance Settings
  maintenance: {
    maintenanceMode: boolean;
    maintenanceMessage: string;
    allowedIPs: string[];
    scheduledMaintenanceEnabled: boolean;
    backupScheduleEnabled: boolean;
  };

  // Feature Flags
  features: {
    betaFeaturesEnabled: boolean;
    experimentalFeaturesEnabled: boolean;
    debugModeEnabled: boolean;
    performanceMonitoringEnabled: boolean;
    errorReportingEnabled: boolean;
  };

  // UI/UX Settings
  ui: {
    darkModeEnabled: boolean;
    compactModeEnabled: boolean;
    animationsEnabled: boolean;
    notificationsEnabled: boolean;
    soundEnabled: boolean;
    language: string;
    timezone: string;
  };
}

export interface SettingCategory {
  id: keyof AppSettings;
  name: string;
  description: string;
  icon: string;
  order: number;
}

export interface SettingField {
  key: string;
  name: string;
  description: string;
  type: 'boolean' | 'string' | 'number' | 'select' | 'multiselect' | 'array';
  defaultValue: any;
  options?: { label: string; value: any }[];
  validation?: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: string;
  };
  dependencies?: {
    field: string;
    value: any;
  }[];
}

export interface SettingsUpdateRequest {
  category: keyof AppSettings;
  settings: Partial<AppSettings[keyof AppSettings]>;
}

export interface SettingsValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
