// Application Settings Types

export interface AppSettings {
  // Payment Gateway Settings
  payments: {
    enabled: boolean;
    provider: 'doku' | 'disabled';
    dokuEnabled: boolean;
    testMode: boolean;
    currency: string;
    commissionRate: number;
    minimumAmount: number;
    maximumAmount: number;
    // Doku specific settings
    dokuClientId: string;
    dokuSecretKey: string;
    dokuEnvironment: 'sandbox' | 'production';
    dokuNotificationUrl: string;
  };

  // Billing Settings
  billing: {
    enabled: boolean;
    invoiceGeneration: boolean;
    automaticBilling: boolean;
    billingCycle: 'monthly' | 'yearly';
    gracePeriodDays: number;
    reminderDays: number[];
  };

  // Subscription Settings
  subscriptions: {
    enabled: boolean;
    allowFreePlan: boolean;
    allowUpgrades: boolean;
    allowDowngrades: boolean;
    prorationEnabled: boolean;
    trialPeriodDays: number;
    cancelationPolicy: 'immediate' | 'end_of_period';
  };

  // Marketplace Settings
  marketplace: {
    enabled: boolean;
    paidNodesEnabled: boolean;
    freeNodesEnabled: boolean;
    nodeApprovalRequired: boolean;
    allowNodeUploads: boolean;
    maxNodeSize: number; // in MB
    allowedFileTypes: string[];
    featuredNodesEnabled: boolean;
    reviewSystemEnabled: boolean;
    ratingSystemEnabled: boolean;
  };

  // Developer Settings
  developer: {
    enabled: boolean;
    nodePublishingEnabled: boolean;
    analyticsEnabled: boolean;
    revenueShareEnabled: boolean;
    maxNodesPerDeveloper: number;
    approvalProcessEnabled: boolean;
    sandboxTestingEnabled: boolean;
  };

  // Workflow Engine Settings
  workflows: {
    // Basic Settings
    enabled: boolean;
    maxWorkflowsPerUser: number;
    maxNodesPerWorkflow: number;
    sharingEnabled: boolean;
    exportEnabled: boolean;
    importEnabled: boolean;

    // Execution Engine Settings
    executionTimeoutMinutes: number;
    maxConcurrentExecutions: number;
    defaultExecutionMode: 'sequential' | 'parallel' | 'optimized';
    retryAttempts: number;
    continueOnError: boolean;
    debugModeEnabled: boolean;

    // Scheduling Settings
    schedulingEnabled: boolean;
    maxScheduledWorkflows: number;
    schedulingIntervalMinutes: number;

    // Performance Settings
    maxConcurrentNodes: number;
    nodeExecutionTimeoutSeconds: number;
    memoryLimitMB: number;
    cpuLimitPercent: number;

    // Storage & Logging
    executionHistoryRetentionDays: number;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    maxLogSizeMB: number;
    enableExecutionMetrics: boolean;

    // Security Settings
    sandboxEnabled: boolean;
    allowExternalConnections: boolean;
    allowFileSystemAccess: boolean;
    allowNetworkAccess: boolean;

    // Advanced Features
    webhooksEnabled: boolean;
    apiIntegrationEnabled: boolean;
    customNodeUploadEnabled: boolean;
    workflowTemplatesEnabled: boolean;
  };

  // Email Settings
  email: {
    enabled: boolean;
    provider: 'smtp' | 'sendgrid' | 'mailgun' | 'disabled';
    verificationEnabled: boolean;
    notificationsEnabled: boolean;
    marketingEmailsEnabled: boolean;
    fromEmail: string;
    fromName: string;
  };

  // Security Settings
  security: {
    twoFactorEnabled: boolean;
    passwordRequirements: {
      minLength: number;
      requireUppercase: boolean;
      requireLowercase: boolean;
      requireNumbers: boolean;
      requireSpecialChars: boolean;
    };
    sessionTimeoutMinutes: number;
    maxLoginAttempts: number;
    lockoutDurationMinutes: number;
  };

  // API Settings
  api: {
    rateLimitEnabled: boolean;
    requestsPerMinute: number;
    requestsPerHour: number;
    apiKeysEnabled: boolean;
    webhooksEnabled: boolean;
    corsEnabled: boolean;
    allowedOrigins: string[];
  };

  // Storage Settings
  storage: {
    provider: 'local' | 'aws' | 'gcp' | 'azure';
    maxFileSize: number; // in MB
    allowedFileTypes: string[];
    compressionEnabled: boolean;
    backupEnabled: boolean;
    retentionDays: number;
  };

  // Analytics Settings
  analytics: {
    enabled: boolean;
    trackingEnabled: boolean;
    dataRetentionDays: number;
    anonymizeData: boolean;
    exportEnabled: boolean;
    realtimeEnabled: boolean;
  };

  // Maintenance Settings
  maintenance: {
    maintenanceMode: boolean;
    maintenanceMessage: string;
    allowedIPs: string[];
    scheduledMaintenanceEnabled: boolean;
    backupScheduleEnabled: boolean;
  };

  // Feature Flags
  features: {
    betaFeaturesEnabled: boolean;
    experimentalFeaturesEnabled: boolean;
    debugModeEnabled: boolean;
    performanceMonitoringEnabled: boolean;
    errorReportingEnabled: boolean;
  };

  // UI/UX Settings
  ui: {
    darkModeEnabled: boolean;
    compactModeEnabled: boolean;
    animationsEnabled: boolean;
    notificationsEnabled: boolean;
    soundEnabled: boolean;
    language: string;
    timezone: string;
  };
}

export interface SettingCategory {
  id: keyof AppSettings;
  name: string;
  description: string;
  icon: string;
  order: number;
}

export interface SettingField {
  key: string;
  name: string;
  description: string;
  type: 'boolean' | 'string' | 'number' | 'select' | 'multiselect' | 'array';
  defaultValue: any;
  options?: { label: string; value: any }[];
  validation?: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: string;
  };
  dependencies?: {
    field: string;
    value: any;
  }[];
}

export interface SettingsUpdateRequest {
  category: keyof AppSettings;
  settings: Partial<AppSettings[keyof AppSettings]>;
}

export interface SettingsValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
