"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Save,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Info,
  LogIn
} from "lucide-react";
import { settingCategories, getCategoryIcon } from "@/lib/settings/settings-config";
import { AppSettings } from "@/lib/settings/types";
import { SettingsCategoryPanel } from "@/components/settings/settings-category-panel";
import { WorkflowSettingsPanel } from "@/components/settings/workflow-settings-panel";
import { useToast } from "@/hooks/use-toast";

export default function SettingsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeCategory, setActiveCategory] = useState('payments');
  const [hasChanges, setHasChanges] = useState(false);
  const [pendingChanges, setPendingChanges] = useState<Record<string, any>>({});
  const [authError, setAuthError] = useState(false);
  const { toast } = useToast();

  // Load settings on component mount and when session changes
  useEffect(() => {
    if (status === 'loading') return; // Still loading session

    if (status === 'unauthenticated') {
      setAuthError(true);
      setLoading(false);
      return;
    }

    if (status === 'authenticated') {
      setAuthError(false);
      loadSettings();
    }
  }, [status]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/settings');

      if (response.status === 401) {
        setAuthError(true);
        return;
      }

      if (response.ok) {
        const data = await response.json();
        setSettings(data);
        setAuthError(false);
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to load settings');
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryChange = (category: string, categorySettings: any) => {
    setPendingChanges(prev => ({
      ...prev,
      [category]: categorySettings
    }));
    setHasChanges(true);
  };

  const saveSettings = async () => {
    if (!hasChanges || Object.keys(pendingChanges).length === 0) return;

    try {
      setSaving(true);

      // Save each category that has changes
      for (const [category, categorySettings] of Object.entries(pendingChanges)) {
        const response = await fetch(`/api/settings/${category}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(categorySettings),
        });

        if (response.status === 401) {
          setAuthError(true);
          toast({
            title: "Authentication Required",
            description: "Please sign in to save settings.",
            variant: "destructive",
          });
          return;
        }

        if (!response.ok) {
          const error = await response.json().catch(() => ({}));
          throw new Error(error.error || `Failed to save ${category} settings`);
        }
      }

      // Reload settings to get the latest state
      await loadSettings();
      setPendingChanges({});
      setHasChanges(false);

      toast({
        title: "Success",
        description: "Settings saved successfully!",
        variant: "default",
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save settings",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const resetSettings = async () => {
    if (!confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      return;
    }

    try {
      setSaving(true);
      const response = await fetch('/api/settings/reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      if (response.ok) {
        await loadSettings();
        setPendingChanges({});
        setHasChanges(false);
        toast({
          title: "Success",
          description: "Settings reset to defaults successfully!",
          variant: "default",
        });
      } else {
        throw new Error('Failed to reset settings');
      }
    } catch (error) {
      console.error('Error resetting settings:', error);
      toast({
        title: "Error",
        description: "Failed to reset settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const discardChanges = () => {
    setPendingChanges({});
    setHasChanges(false);
    toast({
      title: "Changes Discarded",
      description: "All unsaved changes have been discarded.",
      variant: "default",
    });
  };

  // Show authentication error
  if (authError) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbPage>Settings</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col items-center justify-center p-4">
            <div className="text-center space-y-4">
              <LogIn className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h2 className="text-xl font-semibold">Authentication Required</h2>
                <p className="text-muted-foreground mt-2">
                  You need to be logged in to access application settings.
                </p>
              </div>
              <Button onClick={() => router.push('/auth/signin')} className="mt-4">
                <LogIn className="h-4 w-4 mr-2" />
                Sign In
              </Button>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (loading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbPage>Settings</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col items-center justify-center p-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
            <p className="text-muted-foreground">Loading settings...</p>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbPage>Application Settings</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {/* Header Section */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Application Settings</h1>
              <p className="text-muted-foreground">
                Configure system features, payment gateways, and application behavior
              </p>
            </div>
            <div className="flex items-center gap-2">
              {hasChanges && (
                <>
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Info className="h-3 w-3" />
                    Unsaved Changes
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={discardChanges}
                    disabled={saving}
                  >
                    Discard
                  </Button>
                </>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={resetSettings}
                disabled={saving}
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Reset All
              </Button>
              <Button
                size="sm"
                onClick={saveSettings}
                disabled={!hasChanges || saving}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {saving ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>

          {/* Warning Alert */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Important:</strong> Changes to these settings affect the entire application.
              Some changes may require a restart to take effect. Always test in a development environment first.
            </AlertDescription>
          </Alert>

          {/* Settings Layout */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            {/* Category Navigation */}
            <Card className="col-span-2">
              <CardHeader>
                <CardTitle>Categories</CardTitle>
                <CardDescription>
                  Select a category to configure
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-1">
                  {settingCategories.map((category) => {
                    const IconComponent = getCategoryIcon(category.id);
                    const isActive = activeCategory === category.id;
                    return (
                      <button
                        key={category.id}
                        onClick={() => setActiveCategory(category.id)}
                        className={`w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-muted/50 transition-colors ${
                          isActive ? 'bg-muted border-r-2 border-primary' : ''
                        }`}
                      >
                        <div className={`p-2 rounded-lg ${
                          isActive ? 'bg-primary text-primary-foreground' : 'bg-muted'
                        }`}>
                          <IconComponent className="h-4 w-4" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className={`font-medium text-sm ${
                            isActive ? 'text-foreground' : 'text-muted-foreground'
                          }`}>
                            {category.name}
                          </p>
                          <p className="text-xs text-muted-foreground truncate">
                            {category.description}
                          </p>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Settings Panel */}
            <div className="col-span-5">
              {settingCategories.map((category) => (
                <div
                  key={category.id}
                  className={activeCategory === category.id ? 'block' : 'hidden'}
                >
                  {category.id === 'workflows' ? (
                    <WorkflowSettingsPanel
                      category={category}
                      settings={settings}
                      pendingChanges={pendingChanges[category.id]}
                      onChange={(categorySettings) => handleCategoryChange(category.id, categorySettings)}
                    />
                  ) : (
                    <SettingsCategoryPanel
                      category={category}
                      settings={settings}
                      pendingChanges={pendingChanges[category.id]}
                      onChange={(categorySettings) => handleCategoryChange(category.id, categorySettings)}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
